import pandas as pd

def extract_unique_ingredients_by_category(excel_file):
    # 读取Excel文件
    df = pd.read_excel(excel_file)
    
    # 为每个类别创建集合存储唯一成分
    categories = {}
    
    # 处理每一列
    for column in df.columns:
        category_ingredients = set()
        for cell_value in df[column].dropna():
            if pd.notna(cell_value) and str(cell_value).strip():
                # 按逗号分割成分
                ingredients = [ingredient.strip() for ingredient in str(cell_value).split(',')]
                # 添加到该类别的集合中
                category_ingredients.update(ingredients)
        
        # 移除空字符串并排序
        categories[column] = sorted([ingredient for ingredient in category_ingredients if ingredient])
    
    # 找到最长的列表长度，用于对齐
    max_length = max(len(ingredients) for ingredients in categories.values()) if categories else 0
    
    # 创建DataFrame，短列表用空字符串填充
    result_data = {}
    for column, ingredients in categories.items():
        # 填充到相同长度
        padded_ingredients = ingredients + [''] * (max_length - len(ingredients))
        result_data[column] = padded_ingredients
    
    # 创建结果DataFrame
    result_df = pd.DataFrame(result_data)
    
    # 保存为CSV文件
    result_df.to_csv('unique_ingredients_by_category.csv', index=False, encoding='utf-8-sig')
    
    # 打印统计信息
    print("各类别唯一成分统计:")
    for column, ingredients in categories.items():
        print(f"{column}: {len(ingredients)} 个唯一成分")
    
    print(f"\n结果已保存到 unique_ingredients_by_category.csv")
    
    return result_df

if __name__ == "__main__":
    # 处理data.xlsx文件
    result = extract_unique_ingredients_by_category('data.xlsx')
    print("\n前10行预览:")
    print(result.head(10))
